#%%
import os
import datetime
import numpy as np

import sys
sys.path.append('E:/Codes/utils')
from ugm.DGF.time_frequency_generation.generate import generate_spec_cond


def load_nga_west2_data():
    data_path = 'E:/Earthquakes/PEER-NGA-WEST2/NGMF_50gal/'
    conds = np.stack((
        np.load(data_path + 'mag_2844.npy'),
        np.load(data_path + 'dep_2844.npy'),
        np.load(data_path + 'repi_2844.npy'),
        np.load(data_path + 'vs30_2844.npy'),
        ), -1)
    
    conds = np.concatenate((conds, conds), 0)
    specs = np.concatenate((
        np.load(data_path + 'spec128/ew_spectrogram_2844.npy'),
        np.load(data_path + 'spec128/ns_spectrogram_2844.npy'),
        ), 0)
    
    pgas = np.concatenate((
        np.load(data_path + 'pga_ew_2844.npy'),
        np.load(data_path + 'pga_ns_2844.npy'),
        ), 0) * 9.8  # m/s2
    return conds, specs, pgas


conds, specs, pgas = load_nga_west2_data()

#%%
save_path = os.path.join(
    os.path.dirname(__file__), 'save', 
    datetime.datetime.now().strftime('%Y%m%d-%H%M%S')
    )
model_path = 'E:/Codes/TJ/StyleGAN-EQ/code-conditional/pretrained_models/KKIK-cond-c16384-fid2.0140.pkl'

# only generate specs for one direction
# update seed value according datetime
result = generate_spec_cond(
    conds[:2844], model_file=model_path, batch_size=64, 
    seed=int(datetime.datetime.now().strftime('%H%M%S'))
)

os.makedirs(save_path, exist_ok=True)
np.save(os.path.join(save_path, 'c.npy'), result['c'])
np.save(os.path.join(save_path, 'z.npy'), result['z'])
np.save(os.path.join(save_path, 'w.npy'), result['w'])
np.save(os.path.join(save_path, 's.npy'), result['s'])
np.save(os.path.join(save_path, 'pga.npy'), 0.5*(pgas[:2844]+pgas[2844:]))

# %%

import matplotlib.pyplot as plt

plt.figure(figsize=(10, 10))
for i in range(16):
    plt.subplot(4, 4, i+1)
    plt.imshow(result['s'][i], aspect='auto')
    plt.axis('off')
plt.show()

# %% get history with GLA
import os
import scipy.io
import numpy as np
from tqdm import tqdm

import sys
sys.path.append('E:/Codes/utils')
from ugm.converter.GLA import GLA


for f in os.listdir(os.path.join(os.path.dirname(__file__), 'save')):
    save_path = os.path.join(os.path.dirname(__file__), 'save', f)
    if os.path.isdir(save_path):
        print(f)
        print('-------------------')
        if os.path.exists(os.path.join(save_path, 'h.npy')):
            print('history already exists')
            continue
        
        specs = np.load(os.path.join(save_path, 's.npy'))

        h = []
        for i in tqdm(range(specs.shape[0])):
            if i == 0:
                h.append(GLA(specs[i], 128, 10000, return_y=True))
            else:
                h.append(GLA(specs[i], 128, 3000, return_y=True, y_init=h[-1]))
                if i % 100 == 0:
                    np.save(os.path.join(save_path, f'h_{i}.npy'), np.stack(h, 0))

        h = np.stack(h, 0)
        np.save(os.path.join(save_path, 'h.npy'), h)
        scipy.io.savemat(os.path.join(save_path, 'h.mat'), {'h': h})

# %%
# plot example pred v. true

conds, specs, pgas = load_nga_west2_data()
specs_true = 0.5 * (specs[:2844] + specs[2844:])
specs_pred = []

for f in os.listdir(os.path.join(os.path.dirname(__file__), 'save')):
    save_path = os.path.join(os.path.dirname(__file__), 'save', f)
    if os.path.isdir(save_path):
        specs_pred.append(np.load(os.path.join(save_path, 's.npy')))

specs_pred = np.stack(specs_pred, 0).mean(0)


import matplotlib.pyplot as plt

plt.figure(figsize=(10, 10))
for i in range(16):
    plt.subplot(4, 4, i+1)
    plt.imshow(specs_true[100*i], aspect='auto')
    plt.axis('off')
plt.show()

plt.figure(figsize=(10, 10))
for i in range(16):
    plt.subplot(4, 4, i+1)
    plt.imshow(specs_pred[100*i], aspect='auto')
    plt.axis('off')
plt.show()

plt.figure(figsize=(6, 3))
plt.subplot(1, 2, 1)
plt.imshow(specs_true.mean(0))
plt.axis('off')
plt.subplot(1, 2, 2)
plt.imshow(specs_pred.mean(0))
plt.axis('off')
plt.show()

# %% calculate FAS
from ugm.fas import fourier_amplitude_spectra as cal_fas


for f in os.listdir(os.path.join(os.path.dirname(__file__), 'save')):
    save_path = os.path.join(os.path.dirname(__file__), 'save', f)
    if os.path.isdir(save_path):
        if os.path.exists(os.path.join(save_path, 'fas.npy')):
            continue
        h = np.load(os.path.join(save_path, 'h.npy'))
        fas = [cal_fas(h[i], 0.02)[1] for i in range(h.shape[0])]
        fas = np.stack(fas, 0)
        np.save(os.path.join(save_path, 'fas.npy'), fas)


# %%
