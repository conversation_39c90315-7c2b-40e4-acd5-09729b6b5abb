#%%
import sys
sys.path.append('E:/Codes/utils')
from ugm.DGF.intensity_regression.predict import predict_pga, compute_metrics

import os
import numpy as np


def load_nga_west2_data():
    data_path = 'E:/Earthquakes/PEER-NGA-WEST2/NGMF_50gal/'
    conds = np.stack((
        np.load(data_path + 'mag_2844.npy'),
        np.load(data_path + 'dep_2844.npy'),
        np.load(data_path + 'repi_2844.npy'),
        np.load(data_path + 'vs30_2844.npy'),
        ), -1)
    
    conds = np.concatenate((conds, conds), 0)
    specs = np.concatenate((
        np.load(data_path + 'spec128/ew_spectrogram_2844.npy'),
        np.load(data_path + 'spec128/ns_spectrogram_2844.npy'),
        ), 0)
    
    pgas = np.concatenate((
        np.load(data_path + 'pga_ew_2844.npy'),
        np.load(data_path + 'pga_ns_2844.npy'),
        ), 0) * 9.8
    return conds, specs, pgas


def load_nga_west2_fake_data():
    specs_pred = []
    conds_true = []
    pgas_true = []

    for f in os.listdir(os.path.join(os.path.dirname(__file__), 'save')):
        save_path = os.path.join(os.path.dirname(__file__), 'save', f)
        if os.path.isdir(save_path):
            specs_pred.append(np.load(os.path.join(save_path, 's.npy')))
            conds_true.append(np.load(os.path.join(save_path, 'c.npy')))
            pgas_true.append(np.load(os.path.join(save_path, 'pga.npy')))

    specs_pred = np.stack(specs_pred, 0)
    conds_true = np.stack(conds_true, 0)
    pgas_true = np.stack(pgas_true, 0)

    return specs_pred, conds_true, pgas_true

#%%
save_path = os.path.join(os.path.dirname(__file__), 'save')

# conds, specs, pgas = load_nga_west2_data()
specs_pred, conds_true, pgas_true = load_nga_west2_fake_data()

# Run prediction
results = [
    predict_pga(
        cond = conds_true[i],
        spec = specs_pred[i],
        pga = pgas_true[i],
        model_files=[
            'E:/Codes/utils/ugm/DGF/intensity_regression/phys-mha-l3-b.pth',
            'E:/Codes/utils/ugm/DGF/intensity_regression/phys-mha-l3-d.pth',
            ],
        save_path=save_path,
        save_results=False,
        verbose=False
    ) for i in range(specs_pred.shape[0])
]

np.save(
    os.path.join(save_path, 'pga_pred.npy'), 
    np.stack([results[i]['predictions'] for i in range(len(results))], 0).mean(0)
)

#%%

# results = predict_pga(
#     cond = conds,
#     spec = specs,
#     pga = pgas,
#     model_files=[
#         'E:/Codes/utils/ugm/DGF/intensity_regression/phys-mha-l3-b.pth',
#         'E:/Codes/utils/ugm/DGF/intensity_regression/phys-mha-l3-d.pth',
#         'E:/Codes/utils/ugm/DGF/intensity_regression/phys-mha-l3-0.9-b.pth',
#         ],
#     save_path=save_path,
#     save_results=True,
#     verbose=False
# )

#%%
pred = np.stack([results[i]['predictions'] for i in range(len(results))], 0).mean(0)
true = pgas_true.mean(0)

# # pred = results['predictions']
# # true = results['targets']
# pred = 0.5 * (results['predictions'][:2844] + results['predictions'][2844:])
# true = 0.5 * (results['targets'][:2844] + results['targets'][2844:])

mask = true > 0.5
pred = pred[mask]
true = true[mask]
pred[pred<0.5] = 0.5

mask = true < 9
pred = pred[mask]
true = true[mask]
pred[pred>9] = 9

metrics = compute_metrics(pred, true)

print("Evaluation Metrics (Original PGA Scale):")
print(f"  MAE  : {metrics['mae']:.6f}")
print(f"  MAPE : {metrics['mape']:.2f}%")
print(f"  RMSE : {metrics['rmse']:.6f}")
print(f"  Corr.: {metrics['correlation']:.6f}")

# Access results
print(f"\nFinal Results Summary:")
print(f"Number of predictions: {len(pred)}")
print(f"Prediction range: [{pred.min():.6f}, {pred.max():.6f}]")
print(f"Target range: [{true.min():.6f}, {true.max():.6f}]")

# %%
import matplotlib.pyplot as plt

plt.scatter(pred, true, s=4, alpha=.5)
plt.xscale('log')
plt.yscale('log')
plt.gca().set_aspect('equal', adjustable='box')

# plt.scatter(true, np.abs(1-pred/true)*100)

# plt.scatter(conds[:, 2], pred-true, s=4, alpha=.5)
# plt.yscale('log')

# %%
